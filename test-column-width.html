<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Column Width Test - Snap Charts</title>
  <link rel="stylesheet" href="snapapp.css">
  <link rel="stylesheet" href="components/charts/snap-charts.css">
  <style>
    body {
      font-family: 'Amazon Ember', Arial, sans-serif;
      margin: 20px;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .test-section {
      margin-bottom: 40px;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .test-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
      color: #333;
    }
    .test-description {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
    }
    .chart-container {
      width: 100%;
      height: 350px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .compare-controls {
      margin: 10px 0;
    }
    .compare-button {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    .compare-button:hover {
      background: #0056b3;
    }
    .status {
      font-weight: bold;
      padding: 5px 10px;
      border-radius: 4px;
      margin-left: 10px;
    }
    .status.demo { background: #e7f3ff; color: #0066cc; }
    .status.production { background: #fff3e0; color: #cc6600; }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>Column Width Test - 32px Fixed Width</h1>
    <p>Testing that column widths are consistently 32px in both demo and production modes, with and without compare mode.</p>
    
    <!-- Demo Mode Test -->
    <div class="test-section">
      <div class="test-title">Demo Mode (with container) <span class="status demo">DEMO</span></div>
      <div class="test-description">
        This chart should show 32px column widths. Container has border and padding.
      </div>
      <div class="compare-controls">
        <button class="compare-button" onclick="toggleCompare('demo')">Toggle Compare Mode</button>
        <span id="demo-compare-status">Compare: OFF</span>
      </div>
      <div id="demo-chart" class="chart-container"></div>
    </div>
    
    <!-- Production Mode Test -->
    <div class="test-section">
      <div class="test-title">Production Mode (no container) <span class="status production">PRODUCTION</span></div>
      <div class="test-description">
        This chart should show 32px column widths. No container border or padding - direct chart rendering.
      </div>
      <div class="compare-controls">
        <button class="compare-button" onclick="toggleCompare('production')">Toggle Compare Mode</button>
        <span id="production-compare-status">Compare: OFF</span>
      </div>
      <div id="production-chart" class="chart-container"></div>
    </div>
  </div>

  <script src="components/charts/snap-charts.js"></script>
  <script>
    // Test data
    const testData = [
      { date: '2024-01-01', sales: 45, royalties: 180, returns: 2, values: [15, 12, 8, 6, 4] },
      { date: '2024-01-02', sales: 67, royalties: 268, returns: 1, values: [22, 18, 12, 9, 6] },
      { date: '2024-01-03', sales: 34, royalties: 136, returns: 3, values: [12, 10, 6, 4, 2] },
      { date: '2024-01-04', sales: 89, royalties: 356, returns: 0, values: [30, 25, 18, 10, 6] },
      { date: '2024-01-05', sales: 56, royalties: 224, returns: 2, values: [20, 15, 12, 6, 3] }
    ];

    const compareData = [
      { date: '2023-01-01', sales: 38, royalties: 152, returns: 1, values: [13, 10, 7, 5, 3] },
      { date: '2023-01-02', sales: 52, royalties: 208, returns: 2, values: [18, 14, 10, 7, 3] },
      { date: '2023-01-03', sales: 41, royalties: 164, returns: 1, values: [15, 12, 8, 4, 2] },
      { date: '2023-01-04', sales: 73, royalties: 292, returns: 1, values: [25, 20, 15, 8, 5] },
      { date: '2023-01-05', sales: 49, royalties: 196, returns: 3, values: [17, 13, 10, 6, 3] }
    ];

    let demoChart, productionChart;
    let demoCompareMode = false;
    let productionCompareMode = false;

    // Initialize charts
    function initCharts() {
      // Demo mode chart (with container)
      demoChart = new SnapChart({
        container: document.getElementById('demo-chart'),
        type: 'stacked-column',
        data: testData,
        demoOptions: {
          showContainer: true,    // Demo mode - show container
          showTitle: false,
          showDataEditor: false,
          showControls: false,
          showInsights: false
        },
        options: {
          height: 300,
          compareMode: false,
          compareData: compareData
        }
      });

      // Production mode chart (no container)
      productionChart = new SnapChart({
        container: document.getElementById('production-chart'),
        type: 'stacked-column',
        data: testData,
        demoOptions: {
          showContainer: false,   // Production mode - no container
          showTitle: false,
          showDataEditor: false,
          showControls: false,
          showInsights: false
        },
        options: {
          height: 300,
          compareMode: false,
          compareData: compareData
        }
      });

      demoChart.render();
      productionChart.render();
    }

    // Toggle compare mode
    function toggleCompare(mode) {
      if (mode === 'demo') {
        demoCompareMode = !demoCompareMode;
        demoChart.options.compareMode = demoCompareMode;
        demoChart.options.compareData = demoCompareMode ? compareData : null;
        demoChart.render();
        document.getElementById('demo-compare-status').textContent = 
          `Compare: ${demoCompareMode ? 'ON' : 'OFF'}`;
      } else {
        productionCompareMode = !productionCompareMode;
        productionChart.options.compareMode = productionCompareMode;
        productionChart.options.compareData = productionCompareMode ? compareData : null;
        productionChart.render();
        document.getElementById('production-compare-status').textContent = 
          `Compare: ${productionCompareMode ? 'ON' : 'OFF'}`;
      }
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', initCharts);
  </script>
</body>
</html>
